/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as SignupImport } from './routes/signup'
import { Route as ResetPasswordImport } from './routes/reset-password'
import { Route as RecoverPasswordImport } from './routes/recover-password'
import { Route as LoginImport } from './routes/login'
import { Route as LayoutImport } from './routes/_layout'
import { Route as LayoutIndexImport } from './routes/_layout/index'
import { Route as LayoutSettingsImport } from './routes/_layout/settings'
import { Route as LayoutSchoolsImport } from './routes/_layout/schools'
import { Route as LayoutRbacImport } from './routes/_layout/rbac'
import { Route as LayoutItemsImport } from './routes/_layout/items'
import { Route as LayoutFloorsImport } from './routes/_layout/floors'
import { Route as LayoutBuildingsImport } from './routes/_layout/buildings'
import { Route as LayoutAdminImport } from './routes/_layout/admin'
import { Route as LayoutFloorsFloorIdCamerasImport } from './routes/_layout/floors/$floorId/cameras'

// Create/Update Routes

const SignupRoute = SignupImport.update({
  path: '/signup',
  getParentRoute: () => rootRoute,
} as any)

const ResetPasswordRoute = ResetPasswordImport.update({
  path: '/reset-password',
  getParentRoute: () => rootRoute,
} as any)

const RecoverPasswordRoute = RecoverPasswordImport.update({
  path: '/recover-password',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const LayoutRoute = LayoutImport.update({
  id: '/_layout',
  getParentRoute: () => rootRoute,
} as any)

const LayoutIndexRoute = LayoutIndexImport.update({
  path: '/',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSettingsRoute = LayoutSettingsImport.update({
  path: '/settings',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutSchoolsRoute = LayoutSchoolsImport.update({
  path: '/schools',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutRbacRoute = LayoutRbacImport.update({
  path: '/rbac',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutItemsRoute = LayoutItemsImport.update({
  path: '/items',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutFloorsRoute = LayoutFloorsImport.update({
  path: '/floors',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutBuildingsRoute = LayoutBuildingsImport.update({
  path: '/buildings',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutAdminRoute = LayoutAdminImport.update({
  path: '/admin',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutFloorsFloorIdCamerasRoute = LayoutFloorsFloorIdCamerasImport.update(
  {
    path: '/$floorId/cameras',
    getParentRoute: () => LayoutFloorsRoute,
  } as any,
)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_layout': {
      preLoaderRoute: typeof LayoutImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/recover-password': {
      preLoaderRoute: typeof RecoverPasswordImport
      parentRoute: typeof rootRoute
    }
    '/reset-password': {
      preLoaderRoute: typeof ResetPasswordImport
      parentRoute: typeof rootRoute
    }
    '/signup': {
      preLoaderRoute: typeof SignupImport
      parentRoute: typeof rootRoute
    }
    '/_layout/admin': {
      preLoaderRoute: typeof LayoutAdminImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/buildings': {
      preLoaderRoute: typeof LayoutBuildingsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/floors': {
      preLoaderRoute: typeof LayoutFloorsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/items': {
      preLoaderRoute: typeof LayoutItemsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/rbac': {
      preLoaderRoute: typeof LayoutRbacImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/schools': {
      preLoaderRoute: typeof LayoutSchoolsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/settings': {
      preLoaderRoute: typeof LayoutSettingsImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/': {
      preLoaderRoute: typeof LayoutIndexImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/floors/$floorId/cameras': {
      preLoaderRoute: typeof LayoutFloorsFloorIdCamerasImport
      parentRoute: typeof LayoutFloorsImport
    }
  }
}

// Create and export the route tree

export const routeTree = rootRoute.addChildren([
  LayoutRoute.addChildren([
    LayoutAdminRoute,
    LayoutBuildingsRoute,
    LayoutFloorsRoute.addChildren([LayoutFloorsFloorIdCamerasRoute]),
    LayoutItemsRoute,
    LayoutRbacRoute,
    LayoutSchoolsRoute,
    LayoutSettingsRoute,
    LayoutIndexRoute,
  ]),
  LoginRoute,
  RecoverPasswordRoute,
  ResetPasswordRoute,
  SignupRoute,
])

/* prettier-ignore-end */

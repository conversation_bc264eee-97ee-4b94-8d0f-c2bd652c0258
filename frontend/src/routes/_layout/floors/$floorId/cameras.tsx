import { useQuery } from "@tanstack/react-query"
import { createFileRoute } from "@tanstack/react-router"
import { Box, Container, Heading, Text, Spinner, Alert } from "@chakra-ui/react"

import { FloorsService } from "@/client"
import CameraManagement from "@/components/Cameras/CameraManagement"
import Navbar from "@/components/Common/Navbar"

export const Route = createFileRoute("/_layout/floors/$floorId/cameras")({
  component: FloorCameras,
})

function FloorCameras() {
  const { floorId } = Route.useParams()

  const {
    data: floor,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["floors", floorId],
    queryFn: () => FloorsService.readFloor({ floorId }),
  })

  if (isLoading) {
    return (
      <>
        <Navbar type="page" />
        <Container maxW="6xl" py={6}>
          <Box display="flex" justifyContent="center" alignItems="center" h="200px">
            <Spinner size="lg" />
          </Box>
        </Container>
      </>
    )
  }

  if (error || !floor) {
    return (
      <>
        <Navbar type="page" />
        <Container maxW="6xl" py={6}>
          <Alert status="error">
            <Text>Floor not found or error loading floor data.</Text>
          </Alert>
        </Container>
      </>
    )
  }

  return (
    <>
      <Navbar type="page" />
      <CameraManagement floor={floor} />
    </>
  )
}

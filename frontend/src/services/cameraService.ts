import { OpenAPI } from "@/client"

// Temporary camera types until we regenerate the client
export interface Camera {
  id: string
  name: string
  position_x: number
  position_y: number
  floor_id: string
  created_at: string
  updated_at?: string
}

export interface CameraCreate {
  name: string
  position_x: number
  position_y: number
  floor_id: string
}

export interface CameraUpdate {
  name?: string
  position_x?: number
  position_y?: number
}

export interface CamerasResponse {
  data: Camera[]
  count: number
}

class CameraService {
  private baseUrl = `${OpenAPI.BASE}/api/v1/cameras`

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const token = await OpenAPI.TOKEN()
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async getCameras(floorId?: string): Promise<CamerasResponse> {
    const params = new URLSearchParams()
    if (floorId) {
      params.append('floor_id', floorId)
    }
    
    const query = params.toString() ? `?${params.toString()}` : ''
    return this.request<CamerasResponse>(`/${query}`)
  }

  async getCamerasByFloor(floorId: string): Promise<CamerasResponse> {
    return this.request<CamerasResponse>(`/floor/${floorId}`)
  }

  async getCamera(cameraId: string): Promise<Camera> {
    return this.request<Camera>(`/${cameraId}`)
  }

  async createCamera(camera: CameraCreate): Promise<Camera> {
    return this.request<Camera>('/', {
      method: 'POST',
      body: JSON.stringify(camera),
    })
  }

  async updateCamera(cameraId: string, camera: CameraUpdate): Promise<Camera> {
    return this.request<Camera>(`/${cameraId}`, {
      method: 'PUT',
      body: JSON.stringify(camera),
    })
  }

  async deleteCamera(cameraId: string): Promise<{ message: string }> {
    return this.request<{ message: string }>(`/${cameraId}`, {
      method: 'DELETE',
    })
  }
}

export const cameraService = new CameraService()

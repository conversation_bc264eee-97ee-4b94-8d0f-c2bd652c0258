import { useState } from "react"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  Box,
  Button,
  Container,
  Heading,
  HStack,
  Text,
  VStack,
  Switch,
  Table,
  IconButton,
  Input,
} from "@chakra-ui/react"
import { FaEdit, FaTrash, FaPlus } from "react-icons/fa"
import FloorPlanCanvas from "@/components/Floors/FloorPlanCanvas"
import useCustomToast from "@/hooks/useCustomToast"
import { cameraService, type Camera } from "@/services/cameraService"
import type { FloorPublic } from "@/client"

interface CameraManagementProps {
  floor: FloorPublic
}

const CameraManagement = ({ floor }: CameraManagementProps) => {
  const [isAdminMode, setIsAdminMode] = useState(false)
  const [editingCamera, setEditingCamera] = useState<Camera | null>(null)
  const [newCameraName, setNewCameraName] = useState("")
  const queryClient = useQueryClient()
  const { showSuccessToast, showErrorToast } = useCustomToast()

  const { data: camerasResponse, isLoading } = useQuery({
    queryKey: ["cameras", floor.id],
    queryFn: () => cameraService.getCamerasByFloor(floor.id)
  })

  const cameras = camerasResponse?.data || []

  const createCameraMutation = useMutation({
    mutationFn: ({ name, x, y }: { name: string; x: number; y: number }) =>
      cameraService.createCamera({
        name,
        position_x: x,
        position_y: y,
        floor_id: floor.id
      }),
    onSuccess: () => {
      showSuccessToast("Camera added successfully")
      queryClient.invalidateQueries({ queryKey: ["cameras", floor.id] })
      setNewCameraName("")
    },
    onError: () => {
      showErrorToast("Failed to add camera")
    }
  })

  const updateCameraMutation = useMutation({
    mutationFn: ({ id, name, x, y }: { id: string; name?: string; x?: number; y?: number }) => {
      const updateData: any = {}
      if (name !== undefined) updateData.name = name
      if (x !== undefined) updateData.position_x = x
      if (y !== undefined) updateData.position_y = y
      return cameraService.updateCamera(id, updateData)
    },
    onSuccess: () => {
      showSuccessToast("Camera updated successfully")
      queryClient.invalidateQueries({ queryKey: ["cameras", floor.id] })
    },
    onError: () => {
      showErrorToast("Failed to update camera")
    }
  })

  const deleteCameraMutation = useMutation({
    mutationFn: (cameraId: string) => cameraService.deleteCamera(cameraId),
    onSuccess: () => {
      showSuccessToast("Camera deleted successfully")
      queryClient.invalidateQueries({ queryKey: ["cameras", floor.id] })
    },
    onError: () => {
      showErrorToast("Failed to delete camera")
    }
  })

  const handleCameraAdd = (x: number, y: number) => {
    if (!newCameraName.trim()) {
      showErrorToast("Please enter a camera name first")
      return
    }
    createCameraMutation.mutate({ name: newCameraName.trim(), x, y })
  }

  const handleCameraMove = (cameraId: string, x: number, y: number) => {
    updateCameraMutation.mutate({ id: cameraId, x, y })
  }

  const handleCameraClick = (camera: Camera) => {
    if (!isAdminMode) {
      // Handle camera view/details in non-admin mode
      console.log("Camera clicked:", camera)
    }
  }

  const handleDeleteCamera = (cameraId: string) => {
    if (confirm("Are you sure you want to delete this camera?")) {
      deleteCameraMutation.mutate(cameraId)
    }
  }

  return (
    <Container maxW="6xl" py={6}>
      <VStack gap={6} align="stretch">
        {/* Header */}
        <Box>
          <Heading size="lg" mb={2}>
            Camera Management - Floor {floor.number}
          </Heading>
          <Text color="gray.600">
            Manage camera positions on the floor plan
          </Text>
        </Box>

        {/* Controls */}
        <HStack justify="space-between" wrap="wrap" gap={4}>
          <HStack gap={4}>
            <HStack>
              <Text fontSize="sm" fontWeight="medium">Admin Mode:</Text>
              <Switch
                isChecked={isAdminMode}
                onChange={(e) => setIsAdminMode(e.target.checked)}
                colorScheme="blue"
              />
            </HStack>
            
            {isAdminMode && (
              <HStack>
                <Text fontSize="sm">Camera Name:</Text>
                <Input
                  placeholder="Enter camera name"
                  value={newCameraName}
                  onChange={(e) => setNewCameraName(e.target.value)}
                  size="sm"
                  w="200px"
                />
              </HStack>
            )}
          </HStack>

          <Text fontSize="sm" color="gray.500">
            {cameras.length} camera{cameras.length !== 1 ? 's' : ''} on this floor
          </Text>
        </HStack>

        {/* Floor Plan Canvas */}
        <FloorPlanCanvas
          floor={floor}
          cameras={cameras}
          isAdminMode={isAdminMode}
          onCameraAdd={handleCameraAdd}
          onCameraMove={handleCameraMove}
          onCameraClick={handleCameraClick}
        />

        {/* Camera List */}
        {cameras.length > 0 && (
          <Box>
            <Heading size="md" mb={4}>Cameras on this Floor</Heading>
            <Table.Root size="sm">
              <Table.Header>
                <Table.Row>
                  <Table.ColumnHeader>Name</Table.ColumnHeader>
                  <Table.ColumnHeader>Position (X, Y)</Table.ColumnHeader>
                  <Table.ColumnHeader>Created</Table.ColumnHeader>
                  <Table.ColumnHeader>Actions</Table.ColumnHeader>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {cameras.map((camera) => (
                  <Table.Row key={camera.id}>
                    <Table.Cell fontWeight="medium">{camera.name}</Table.Cell>
                    <Table.Cell>
                      ({camera.position_x.toFixed(1)}%, {camera.position_y.toFixed(1)}%)
                    </Table.Cell>
                    <Table.Cell>
                      {new Date(camera.created_at).toLocaleDateString()}
                    </Table.Cell>
                    <Table.Cell>
                      <HStack gap={2}>
                        <IconButton
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingCamera(camera)}
                        >
                          <FaEdit />
                        </IconButton>
                        <IconButton
                          size="sm"
                          variant="outline"
                          colorScheme="red"
                          onClick={() => handleDeleteCamera(camera.id)}
                        >
                          <FaTrash />
                        </IconButton>
                      </HStack>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table.Root>
          </Box>
        )}

        {/* Instructions */}
        <Box p={4} bg="blue.50" borderRadius="md" border="1px solid" borderColor="blue.200">
          <Text fontSize="sm" color="blue.800">
            <strong>Instructions:</strong>
            {isAdminMode ? (
              <>
                {" "}Enter a camera name above, then click anywhere on the floor plan to add a camera. 
                Drag existing cameras to reposition them.
              </>
            ) : (
              " Toggle Admin Mode to add and manage cameras. Click on cameras to view details."
            )}
          </Text>
        </Box>
      </VStack>
    </Container>
  )
}

export default CameraManagement

import { Box } from "@chakra-ui/react"
import { FaVideo } from "react-icons/fa"

interface CameraIconProps {
  size?: number
  color?: string
  isDragging?: boolean
  onClick?: () => void
  style?: React.CSSProperties
}

const CameraIcon = ({ 
  size = 24, 
  color = "#3182ce", 
  isDragging = false,
  onClick,
  style 
}: CameraIconProps) => {
  return (
    <Box
      display="inline-flex"
      alignItems="center"
      justifyContent="center"
      w={`${size + 8}px`}
      h={`${size + 8}px`}
      bg="white"
      border="2px solid"
      borderColor={color}
      borderRadius="50%"
      cursor={onClick ? "pointer" : isDragging ? "grabbing" : "grab"}
      boxShadow="0 2px 8px rgba(0,0,0,0.15)"
      transition="all 0.2s"
      opacity={isDragging ? 0.7 : 1}
      transform={isDragging ? "scale(1.1)" : "scale(1)"}
      _hover={onClick ? {
        transform: "scale(1.05)",
        boxShadow: "0 4px 12px rgba(0,0,0,0.2)"
      } : {}}
      onClick={onClick}
      style={style}
    >
      <FaVideo size={size} color={color} />
    </Box>
  )
}

export default CameraIcon

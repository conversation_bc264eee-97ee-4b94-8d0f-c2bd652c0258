import { useRef, useState, useCallback } from "react"
import { Box, Image, Text, VStack } from "@chakra-ui/react"
import CameraIcon from "@/components/Common/CameraIcon"
import { getFloorPlanImageUrl } from "@/utils/floor"
import type { Camera } from "@/services/cameraService"
import type { FloorPublic } from "@/client"

interface FloorPlanCanvasProps {
  floor: FloorPublic
  cameras: Camera[]
  isAdminMode?: boolean
  onCameraAdd?: (x: number, y: number) => void
  onCameraMove?: (cameraId: string, x: number, y: number) => void
  onCameraClick?: (camera: Camera) => void
}

const FloorPlanCanvas = ({
  floor,
  cameras,
  isAdminMode = false,
  onCameraAdd,
  onCameraMove,
  onCameraClick
}: FloorPlanCanvasProps) => {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [draggedCamera, setDraggedCamera] = useState<string | null>(null)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })

  const floorPlanUrl = getFloorPlanImageUrl(floor)

  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (!isAdminMode || !onCameraAdd || !canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const x = ((event.clientX - rect.left) / rect.width) * 100
    const y = ((event.clientY - rect.top) / rect.height) * 100

    onCameraAdd(x, y)
  }, [isAdminMode, onCameraAdd])

  const handleCameraMouseDown = useCallback((event: React.MouseEvent, cameraId: string) => {
    if (!isAdminMode) return

    event.stopPropagation()
    setDraggedCamera(cameraId)

    const camera = cameras.find(c => c.id === cameraId)
    if (camera && canvasRef.current) {
      const rect = canvasRef.current.getBoundingClientRect()
      const cameraX = (camera.position_x / 100) * rect.width
      const cameraY = (camera.position_y / 100) * rect.height
      
      setDragOffset({
        x: event.clientX - rect.left - cameraX,
        y: event.clientY - rect.top - cameraY
      })
    }
  }, [isAdminMode, cameras])

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!draggedCamera || !onCameraMove || !canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const x = Math.max(0, Math.min(100, ((event.clientX - rect.left - dragOffset.x) / rect.width) * 100))
    const y = Math.max(0, Math.min(100, ((event.clientY - rect.top - dragOffset.y) / rect.height) * 100))

    onCameraMove(draggedCamera, x, y)
  }, [draggedCamera, onCameraMove, dragOffset])

  const handleMouseUp = useCallback(() => {
    setDraggedCamera(null)
    setDragOffset({ x: 0, y: 0 })
  }, [])

  if (!floorPlanUrl) {
    return (
      <Box
        w="100%"
        h="400px"
        bg="gray.100"
        display="flex"
        alignItems="center"
        justifyContent="center"
        borderRadius="md"
        border="2px dashed"
        borderColor="gray.300"
      >
        <VStack>
          <Text color="gray.500" fontSize="lg">No floor plan image</Text>
          <Text color="gray.400" fontSize="sm">Upload a floor plan to add cameras</Text>
        </VStack>
      </Box>
    )
  }

  return (
    <Box
      ref={canvasRef}
      position="relative"
      w="100%"
      h="400px"
      borderRadius="md"
      overflow="hidden"
      border="1px solid"
      borderColor="gray.200"
      cursor={isAdminMode ? "crosshair" : "default"}
      onClick={handleCanvasClick}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {/* Floor plan image */}
      <Image
        src={floorPlanUrl}
        alt={`Floor ${floor.number} plan`}
        w="100%"
        h="100%"
        objectFit="contain"
        bg="white"
        userSelect="none"
        pointerEvents="none"
      />

      {/* Camera positions */}
      {cameras.map((camera) => (
        <Box
          key={camera.id}
          position="absolute"
          left={`${camera.position_x}%`}
          top={`${camera.position_y}%`}
          transform="translate(-50%, -50%)"
          zIndex={draggedCamera === camera.id ? 1000 : 10}
        >
          <Box
            onMouseDown={(event: React.MouseEvent) => handleCameraMouseDown(event, camera.id)}
          >
            <CameraIcon
              size={20}
              isDragging={draggedCamera === camera.id}
              onClick={!isAdminMode ? () => onCameraClick?.(camera) : undefined}
              style={{
                cursor: isAdminMode ? "grab" : "pointer"
              }}
            />
          </Box>
          {/* Camera label */}
          <Box
            position="absolute"
            top="100%"
            left="50%"
            transform="translateX(-50%)"
            mt={1}
            px={2}
            py={1}
            bg="blackAlpha.700"
            color="white"
            fontSize="xs"
            borderRadius="sm"
            whiteSpace="nowrap"
            pointerEvents="none"
          >
            {camera.name}
          </Box>
        </Box>
      ))}

      {/* Admin mode indicator */}
      {isAdminMode && (
        <Box
          position="absolute"
          top={2}
          right={2}
          px={3}
          py={1}
          bg="blue.500"
          color="white"
          fontSize="sm"
          borderRadius="md"
          fontWeight="medium"
        >
          Admin Mode: Click to add camera
        </Box>
      )}
    </Box>
  )
}

export default FloorPlanCanvas

from fastapi import APIRouter

from app.api.routes import items, login, private, users, utils, schools, threat_detection, rbac, buildings, floors, cameras
from app.core.config import settings

api_router = APIRouter()
api_router.include_router(login.router)
api_router.include_router(users.router)
api_router.include_router(utils.router)
api_router.include_router(items.router)
api_router.include_router(schools.router)
api_router.include_router(buildings.router)
api_router.include_router(floors.router)
api_router.include_router(cameras.router)
api_router.include_router(threat_detection.router)
api_router.include_router(rbac.router)


if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)

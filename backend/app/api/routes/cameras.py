import uuid
from typing import Any
from datetime import datetime

from fastapi import APIRouter, HTTPException
from sqlmodel import func, select

from app.api.deps import CurrentUser, SessionDep
from app.models import <PERSON>, CameraCreate, CameraPublic, CamerasPublic, CameraUpdate, Message, Floor

router = APIRouter(prefix="/cameras", tags=["cameras"])


@router.get("/", response_model=CamerasPublic)
def read_cameras(
    session: SessionDep, 
    current_user: CurrentUser,
    skip: int = 0, 
    limit: int = 100,
    floor_id: uuid.UUID | None = None,
    include_deleted: bool = False
) -> Any:
    """
    Retrieve cameras.
    """
    query = select(Camera)
    count_query = select(func.count()).select_from(Camera)
    
    if not include_deleted:
        query = query.where(Camera.is_deleted == False)
        count_query = count_query.where(Camera.is_deleted == False)
    
    if floor_id:
        query = query.where(Camera.floor_id == floor_id)
        count_query = count_query.where(Camera.floor_id == floor_id)
    
    count = session.exec(count_query).one()
    cameras = session.exec(query.offset(skip).limit(limit)).all()
    
    return CamerasPublic(data=cameras, count=count)


@router.get("/{camera_id}", response_model=CameraPublic)
def read_camera(
    camera_id: uuid.UUID, 
    session: SessionDep, 
    current_user: CurrentUser
) -> Any:
    """
    Get camera by ID.
    """
    camera = session.get(Camera, camera_id)
    if not camera or camera.is_deleted:
        raise HTTPException(status_code=404, detail="Camera not found")
    return camera


@router.post("/", response_model=CameraPublic)
def create_camera(
    *, 
    session: SessionDep, 
    current_user: CurrentUser, 
    camera_in: CameraCreate
) -> Any:
    """
    Create new camera.
    """
    # Verify floor exists and is not deleted
    floor = session.get(Floor, camera_in.floor_id)
    if not floor or floor.is_deleted:
        raise HTTPException(status_code=404, detail="Floor not found")
    
    camera = Camera.model_validate(camera_in)
    session.add(camera)
    session.commit()
    session.refresh(camera)
    return camera


@router.put("/{camera_id}", response_model=CameraPublic)
def update_camera(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    camera_id: uuid.UUID,
    camera_in: CameraUpdate,
) -> Any:
    """
    Update camera.
    """
    camera = session.get(Camera, camera_id)
    if not camera or camera.is_deleted:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    update_dict = camera_in.model_dump(exclude_unset=True)
    if update_dict:
        update_dict["updated_at"] = datetime.utcnow()
        camera.sqlmodel_update(update_dict)
        session.add(camera)
        session.commit()
        session.refresh(camera)
    return camera


@router.delete("/{camera_id}")
def delete_camera(
    session: SessionDep, 
    current_user: CurrentUser, 
    camera_id: uuid.UUID
) -> Message:
    """
    Delete camera (soft delete).
    """
    camera = session.get(Camera, camera_id)
    if not camera or camera.is_deleted:
        raise HTTPException(status_code=404, detail="Camera not found")
    
    camera.is_deleted = True
    camera.deleted_at = datetime.utcnow()
    session.add(camera)
    session.commit()
    return Message(message="Camera deleted successfully")


@router.get("/floor/{floor_id}", response_model=CamerasPublic)
def read_cameras_by_floor(
    floor_id: uuid.UUID,
    session: SessionDep, 
    current_user: CurrentUser,
    include_deleted: bool = False
) -> Any:
    """
    Get all cameras for a specific floor.
    """
    # Verify floor exists
    floor = session.get(Floor, floor_id)
    if not floor or floor.is_deleted:
        raise HTTPException(status_code=404, detail="Floor not found")
    
    query = select(Camera).where(Camera.floor_id == floor_id)
    count_query = select(func.count()).select_from(Camera).where(Camera.floor_id == floor_id)
    
    if not include_deleted:
        query = query.where(Camera.is_deleted == False)
        count_query = count_query.where(Camera.is_deleted == False)
    
    count = session.exec(count_query).one()
    cameras = session.exec(query).all()
    
    return CamerasPublic(data=cameras, count=count)

"""update_camera_model_for_coordinates

Revision ID: ba227b716f5c
Revises: replace_floor_plan_image
Create Date: 2025-06-10 18:05:55.867256

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'ba227b716f5c'
down_revision = 'replace_floor_plan_image'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('camera', sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False))
    op.add_column('camera', sa.Column('position_x', sa.Float(), nullable=False))
    op.add_column('camera', sa.Column('position_y', sa.Float(), nullable=False))
    op.drop_column('camera', 'position')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('camera', sa.Column('position', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.drop_column('camera', 'position_y')
    op.drop_column('camera', 'position_x')
    op.drop_column('camera', 'name')
    # ### end Alembic commands ###

# Camera Management System

This document describes the camera management system that allows administrators to add, position, and manage cameras on floor plans using drag-and-drop functionality.

## Features

### 🎯 **Core Functionality**
- **Drag & Drop Camera Placement**: Click anywhere on a floor plan to add cameras
- **Real-time Position Updates**: Drag cameras to reposition them instantly
- **Visual Camera Indicators**: Camera icons with labels showing camera names
- **Admin Mode Toggle**: Switch between view and edit modes
- **Camera Management**: Full CRUD operations for cameras

### 🏗️ **Architecture**

#### Backend Components
- **Camera Model**: Updated with `name`, `position_x`, `position_y` fields (percentage-based coordinates)
- **Camera API Routes**: Full REST API at `/api/v1/cameras/`
- **Database Migration**: `ba227b716f5c_update_camera_model_for_coordinates.py`

#### Frontend Components
- **CameraManagement**: Main admin panel component
- **FloorPlanCanvas**: Interactive canvas with drag-and-drop functionality
- **CameraIcon**: Reusable camera visual component
- **Camera Service**: API client for camera operations

## 🚀 Setup Instructions

### 1. Run Database Migration
```bash
cd backend
PYTHONPATH=/path/to/backend alembic upgrade head
```

### 2. Start Backend
```bash
cd backend
uvicorn app.main:app --reload
```

### 3. Start Frontend
```bash
cd frontend
npm run dev
```

## 📱 Usage

### Admin Mode
1. Navigate to **Floors** page
2. Click the **camera icon** or **"Manage Cameras"** link for any floor
3. Toggle **Admin Mode** switch to ON
4. Enter a **camera name** in the input field
5. **Click anywhere** on the floor plan to add a camera at that position
6. **Drag existing cameras** to reposition them
7. Use the **camera list table** to edit names or delete cameras

### View Mode
1. Toggle **Admin Mode** switch to OFF
2. **Click on camera icons** to view camera details (future: live feed)
3. View camera positions and information

## 🎨 UI Components

### FloorPlanCanvas
```typescript
<FloorPlanCanvas
  floor={floor}
  cameras={cameras}
  isAdminMode={isAdminMode}
  onCameraAdd={(x, y) => handleCameraAdd(x, y)}
  onCameraMove={(id, x, y) => handleCameraMove(id, x, y)}
  onCameraClick={(camera) => handleCameraClick(camera)}
/>
```

### CameraIcon
```typescript
<CameraIcon
  size={20}
  color="#3182ce"
  isDragging={false}
  onClick={() => handleClick()}
/>
```

## 🔧 API Endpoints

### Camera Management
- `GET /api/v1/cameras/` - List all cameras
- `GET /api/v1/cameras/{camera_id}` - Get camera by ID
- `POST /api/v1/cameras/` - Create new camera
- `PUT /api/v1/cameras/{camera_id}` - Update camera
- `DELETE /api/v1/cameras/{camera_id}` - Delete camera
- `GET /api/v1/cameras/floor/{floor_id}` - Get cameras by floor

### Request/Response Examples

#### Create Camera
```json
POST /api/v1/cameras/
{
  "name": "Main Entrance Camera",
  "position_x": 25.5,
  "position_y": 30.2,
  "floor_id": "123e4567-e89b-12d3-a456-************"
}
```

#### Update Camera Position
```json
PUT /api/v1/cameras/{camera_id}
{
  "position_x": 35.0,
  "position_y": 45.0
}
```

## 📊 Database Schema

### Camera Table
```sql
CREATE TABLE camera (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    position_x FLOAT NOT NULL,  -- X coordinate (0-100%)
    position_y FLOAT NOT NULL,  -- Y coordinate (0-100%)
    floor_id UUID NOT NULL REFERENCES floor(id),
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP
);
```

## 🎯 Coordinate System

- **Position coordinates** are stored as **percentages** (0-100)
- **position_x**: Horizontal position from left edge (0% = left, 100% = right)
- **position_y**: Vertical position from top edge (0% = top, 100% = bottom)
- This ensures cameras maintain relative positions regardless of floor plan image size

## 🔮 Future Enhancements

### Planned Features
- **Live Camera Feeds**: Click cameras to view real-time video
- **Camera Status Indicators**: Online/offline status
- **Camera Groups**: Organize cameras by zones
- **Bulk Operations**: Select multiple cameras for batch actions
- **Camera Templates**: Predefined camera layouts
- **Export/Import**: Save and load camera configurations

### Integration Points
- **Threat Detection**: Link cameras to threat detection system
- **Recording System**: Connect to video recording infrastructure
- **Access Control**: Role-based camera management permissions

## 🛠️ Technical Details

### Coordinate Calculation
```typescript
// Convert click position to percentage
const x = ((event.clientX - rect.left) / rect.width) * 100
const y = ((event.clientY - rect.top) / rect.height) * 100

// Convert percentage back to pixels for display
const pixelX = (camera.position_x / 100) * canvasWidth
const pixelY = (camera.position_y / 100) * canvasHeight
```

### Drag & Drop Implementation
- Uses React mouse events for drag detection
- Calculates relative positions within canvas bounds
- Updates camera positions in real-time via API
- Provides visual feedback during dragging

## 📝 Notes

- Camera positions are **automatically saved** when moved
- **Admin mode** is required for adding/editing cameras
- Camera names must be **unique per floor**
- Deleted cameras are **soft-deleted** (can be recovered)
- All operations require **authentication**

## 🐛 Troubleshooting

### Common Issues
1. **Cameras not appearing**: Check if floor has a valid floor plan image
2. **Drag not working**: Ensure Admin Mode is enabled
3. **API errors**: Verify backend is running and accessible
4. **Position drift**: Clear browser cache and refresh

### Debug Mode
Enable debug logging by setting `localStorage.debug = 'camera:*'` in browser console.
